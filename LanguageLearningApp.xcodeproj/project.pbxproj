// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		5AB556932DE5C73900366632 /* SwiftyBeaver in Frameworks */ = {isa = PBXBuildFile; productRef = 5AB556922DE5C73900366632 /* SwiftyBeaver */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9A6BFE8A2DBFCEAE00234F25 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9A6BFE732DBFCEAC00234F25 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9A6BFE7A2DBFCEAC00234F25;
			remoteInfo = LanguageLearningApp;
		};
		9A6BFE942DBFCEAE00234F25 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9A6BFE732DBFCEAC00234F25 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9A6BFE7A2DBFCEAC00234F25;
			remoteInfo = LanguageLearningApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		9A6BFE7B2DBFCEAC00234F25 /* LanguageLearningApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LanguageLearningApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9A6BFE892DBFCEAE00234F25 /* LanguageLearningAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LanguageLearningAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9A6BFE932DBFCEAE00234F25 /* LanguageLearningAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LanguageLearningAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		9A6BFF6D2DC1040700234F25 /* Exceptions for "LanguageLearningApp" folder in "LanguageLearningApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9A6BFE7A2DBFCEAC00234F25 /* LanguageLearningApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		9A6BFE7D2DBFCEAC00234F25 /* LanguageLearningApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9A6BFF6D2DC1040700234F25 /* Exceptions for "LanguageLearningApp" folder in "LanguageLearningApp" target */,
			);
			path = LanguageLearningApp;
			sourceTree = "<group>";
		};
		9A6BFE8C2DBFCEAE00234F25 /* LanguageLearningAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LanguageLearningAppTests;
			sourceTree = "<group>";
		};
		9A6BFE962DBFCEAE00234F25 /* LanguageLearningAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LanguageLearningAppUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		9A6BFE782DBFCEAC00234F25 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5AB556932DE5C73900366632 /* SwiftyBeaver in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE862DBFCEAE00234F25 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE902DBFCEAE00234F25 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9A6BFE722DBFCEAC00234F25 = {
			isa = PBXGroup;
			children = (
				9A6BFE7D2DBFCEAC00234F25 /* LanguageLearningApp */,
				9A6BFE8C2DBFCEAE00234F25 /* LanguageLearningAppTests */,
				9A6BFE962DBFCEAE00234F25 /* LanguageLearningAppUITests */,
				9A6BFE7C2DBFCEAC00234F25 /* Products */,
			);
			sourceTree = "<group>";
		};
		9A6BFE7C2DBFCEAC00234F25 /* Products */ = {
			isa = PBXGroup;
			children = (
				9A6BFE7B2DBFCEAC00234F25 /* LanguageLearningApp.app */,
				9A6BFE892DBFCEAE00234F25 /* LanguageLearningAppTests.xctest */,
				9A6BFE932DBFCEAE00234F25 /* LanguageLearningAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9A6BFE7A2DBFCEAC00234F25 /* LanguageLearningApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9A6BFE9D2DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningApp" */;
			buildPhases = (
				9A6BFE772DBFCEAC00234F25 /* Sources */,
				9A6BFE782DBFCEAC00234F25 /* Frameworks */,
				9A6BFE792DBFCEAC00234F25 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9A6BFE7D2DBFCEAC00234F25 /* LanguageLearningApp */,
			);
			name = LanguageLearningApp;
			packageProductDependencies = (
				5AB556922DE5C73900366632 /* SwiftyBeaver */,
			);
			productName = LanguageLearningApp;
			productReference = 9A6BFE7B2DBFCEAC00234F25 /* LanguageLearningApp.app */;
			productType = "com.apple.product-type.application";
		};
		9A6BFE882DBFCEAE00234F25 /* LanguageLearningAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9A6BFEA02DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningAppTests" */;
			buildPhases = (
				9A6BFE852DBFCEAE00234F25 /* Sources */,
				9A6BFE862DBFCEAE00234F25 /* Frameworks */,
				9A6BFE872DBFCEAE00234F25 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9A6BFE8B2DBFCEAE00234F25 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9A6BFE8C2DBFCEAE00234F25 /* LanguageLearningAppTests */,
			);
			name = LanguageLearningAppTests;
			packageProductDependencies = (
			);
			productName = LanguageLearningAppTests;
			productReference = 9A6BFE892DBFCEAE00234F25 /* LanguageLearningAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		9A6BFE922DBFCEAE00234F25 /* LanguageLearningAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9A6BFEA32DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningAppUITests" */;
			buildPhases = (
				9A6BFE8F2DBFCEAE00234F25 /* Sources */,
				9A6BFE902DBFCEAE00234F25 /* Frameworks */,
				9A6BFE912DBFCEAE00234F25 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9A6BFE952DBFCEAE00234F25 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9A6BFE962DBFCEAE00234F25 /* LanguageLearningAppUITests */,
			);
			name = LanguageLearningAppUITests;
			packageProductDependencies = (
			);
			productName = LanguageLearningAppUITests;
			productReference = 9A6BFE932DBFCEAE00234F25 /* LanguageLearningAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9A6BFE732DBFCEAC00234F25 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					9A6BFE7A2DBFCEAC00234F25 = {
						CreatedOnToolsVersion = 16.3;
					};
					9A6BFE882DBFCEAE00234F25 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 9A6BFE7A2DBFCEAC00234F25;
					};
					9A6BFE922DBFCEAE00234F25 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 9A6BFE7A2DBFCEAC00234F25;
					};
				};
			};
			buildConfigurationList = 9A6BFE762DBFCEAC00234F25 /* Build configuration list for PBXProject "LanguageLearningApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				yue,
				"zh-Hant",
			);
			mainGroup = 9A6BFE722DBFCEAC00234F25;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				5AB556912DE5C73900366632 /* XCRemoteSwiftPackageReference "SwiftyBeaver" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 9A6BFE7C2DBFCEAC00234F25 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9A6BFE7A2DBFCEAC00234F25 /* LanguageLearningApp */,
				9A6BFE882DBFCEAE00234F25 /* LanguageLearningAppTests */,
				9A6BFE922DBFCEAE00234F25 /* LanguageLearningAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9A6BFE792DBFCEAC00234F25 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE872DBFCEAE00234F25 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE912DBFCEAE00234F25 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9A6BFE772DBFCEAC00234F25 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE852DBFCEAE00234F25 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9A6BFE8F2DBFCEAE00234F25 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9A6BFE8B2DBFCEAE00234F25 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9A6BFE7A2DBFCEAC00234F25 /* LanguageLearningApp */;
			targetProxy = 9A6BFE8A2DBFCEAE00234F25 /* PBXContainerItemProxy */;
		};
		9A6BFE952DBFCEAE00234F25 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9A6BFE7A2DBFCEAC00234F25 /* LanguageLearningApp */;
			targetProxy = 9A6BFE942DBFCEAE00234F25 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		9A6BFE9B2DBFCEAE00234F25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = D55B8T9HW9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9A6BFE9C2DBFCEAE00234F25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = D55B8T9HW9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		9A6BFE9E2DBFCEAE00234F25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LanguageLearningApp/LanguageLearningApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LanguageLearningApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Language Learning";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "我们需要访问您的麦克风来录制您的口语练习。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "我们需要访问您的照片库来保存练习记录。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "我们需要访问您的照片库来保存练习记录。";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "我们需要访问您的语音识别功能来帮助您进行口语练习和发音评估。";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "armv7 microphone";
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunterx.LanguageLearningApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9A6BFE9F2DBFCEAE00234F25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LanguageLearningApp/LanguageLearningApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LanguageLearningApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Language Learning";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "我们需要访问您的麦克风来录制您的口语练习。";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "我们需要访问您的照片库来保存练习记录。";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "我们需要访问您的照片库来保存练习记录。";
				INFOPLIST_KEY_NSSpeechRecognitionUsageDescription = "我们需要访问您的语音识别功能来帮助您进行口语练习和发音评估。";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "armv7 microphone";
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunterx.LanguageLearningApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		9A6BFEA12DBFCEAE00234F25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunter.LanguageLearningAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LanguageLearningApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LanguageLearningApp";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9A6BFEA22DBFCEAE00234F25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunter.LanguageLearningAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LanguageLearningApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LanguageLearningApp";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		9A6BFEA42DBFCEAE00234F25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunter.LanguageLearningAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LanguageLearningApp;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		9A6BFEA52DBFCEAE00234F25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = RST57S693T;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.hunter.LanguageLearningAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LanguageLearningApp;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9A6BFE762DBFCEAC00234F25 /* Build configuration list for PBXProject "LanguageLearningApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9A6BFE9B2DBFCEAE00234F25 /* Debug */,
				9A6BFE9C2DBFCEAE00234F25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9A6BFE9D2DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9A6BFE9E2DBFCEAE00234F25 /* Debug */,
				9A6BFE9F2DBFCEAE00234F25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9A6BFEA02DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9A6BFEA12DBFCEAE00234F25 /* Debug */,
				9A6BFEA22DBFCEAE00234F25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9A6BFEA32DBFCEAE00234F25 /* Build configuration list for PBXNativeTarget "LanguageLearningAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9A6BFEA42DBFCEAE00234F25 /* Debug */,
				9A6BFEA52DBFCEAE00234F25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		5AB556912DE5C73900366632 /* XCRemoteSwiftPackageReference "SwiftyBeaver" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyBeaver/SwiftyBeaver";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.1.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		5AB556922DE5C73900366632 /* SwiftyBeaver */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5AB556912DE5C73900366632 /* XCRemoteSwiftPackageReference "SwiftyBeaver" */;
			productName = SwiftyBeaver;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 9A6BFE732DBFCEAC00234F25 /* Project object */;
}
