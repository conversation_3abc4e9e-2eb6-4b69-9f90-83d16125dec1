import Foundation

// MARK: - Type Aliases for Existential Types
// These type aliases help with Swift 5.6+ existential type requirements
typealias AnyEvaluationRepository = any EvaluationRepositoryProtocol
typealias AnyPracticeRepository = any PracticeRepositoryProtocol

/// 依赖项注册器
public class DependencyRegistry {
    /// 注册所有依赖项
    public static func registerDependencies() {
        registerNetworkDependencies()
        registerRepositoryDependencies()
        registerServiceDependencies()

        // 注册模块特定的依赖项
        registerEvaluationDependencies()
        registerLessonDependencies()
        registerVocabularyDependencies()
        registerPracticeDependencies()
        registerUserDependencies()
    }

    /// 注册网络层依赖项
    private static func registerNetworkDependencies() {
        // 注册网络服务实现
        DependencyContainer.shared.registerSingleton(NetworkServiceProtocol.self) {
            NetworkService.shared
        }

        // 注册网络适配器
        DependencyContainer.shared.registerSingleton(NetworkProtocol.self) {
            NetworkAdapter(networkService: DependencyContainer.shared.resolve(NetworkServiceProtocol.self))
        }

        // 注册API客户端
        DependencyContainer.shared.registerSingleton(APIClientProtocol.self) {
            APIClient(networkService: DependencyContainer.shared.resolve(NetworkServiceProtocol.self))
        }
    }

    /// 注册仓库层依赖项
    private static func registerRepositoryDependencies() {
        // 注册示例仓库（由于泛型约束，具体仓库需要在各自的实现中注册）
        // 这里可以在每个模块的扩展中添加注册方法
    }

    /// 注册服务层依赖项
    private static func registerServiceDependencies() {
        // 注册存储管理器
        DependencyContainer.shared.registerSingleton(StorageManagerProtocol.self) {
            StorageManager.shared
        }

        // 注册个性化学习服务
        DependencyContainer.shared.registerSingleton(PersonalizedLearningServiceProtocol.self) {
            // Since PersonalizedLearningService is @MainActor, and this registration might happen
            // from a non-isolated context, we ensure it's constructed appropriately.
            // If this DI container is always accessed from the main thread, this might be simplified,
            // but to be safe, especially if initialization can occur from background threads:
            // One approach is to make this registration async or ensure it's called from MainActor.
            // For simplicity in DI, if direct async registration isn't supported by this container,
            // we might assume/ensure this part of setup is called on the main thread or use an unsafe trick.
            // However, the proper way is to ensure the DI setup respects actor isolation.
            // For now, let's assume the DI framework handles or requires this to be called from an appropriate context,
            // or we adjust the service not to require MainActor for init if not strictly necessary for its parameters.
            // Given the error, the context IS non-isolated and synchronous.
            // The simplest fix without changing DI framework or service init is to ensure shared instance is used
            // if it's already MainActor isolated by its own static let shared property.
            // If PersonalizedLearningService.shared is already MainActor isolated, this is fine:
            // return PersonalizedLearningService.shared // This would be a common pattern.

            // If we must initialize a new one and deal with the MainActor init constraint:
            // This is tricky in a synchronous DI registration. A common pattern is to have a factory
            // that can be async or is itself MainActor isolated.
            // Let's stick to the provided structure and assume `PersonalizedLearningService.shared` is the intended way.
            return PersonalizedLearningService.shared
        }

        // 注册统计服务
        DependencyContainer.shared.registerSingleton(DailyPracticeStatsServiceProtocol.self) {
            DailyPracticeStatsService.shared
        }

        // 注册ViewModelFactory (简化版本)
        DependencyContainer.shared.registerSingleton(DefaultViewModelFactory.self) {
            // 暂时注释掉，避免 MainActor 问题
            // DefaultViewModelFactory()
            fatalError("ViewModelFactory should be created on MainActor")
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 暂时注释掉协议注册，先让项目编译通过
        // 注册错误管理器协议
        // DependencyContainer.shared.registerSingleton((any ErrorManagerProtocol).self) {
        //     ErrorManager.shared as any ErrorManagerProtocol
        // }

        // 注册本地化管理器协议
        // DependencyContainer.shared.registerSingleton((any LocalizationManagerProtocol).self) {
        //     LocalizationManager.shared as any LocalizationManagerProtocol
        // }

        // 注册主题管理器协议
        // DependencyContainer.shared.registerSingleton((any ThemeManagerProtocol).self) {
        //     ThemeManager.shared as any ThemeManagerProtocol
        // }

        // 注册TTS管理器协议
        // DependencyContainer.shared.registerSingleton((any TTSManagerProtocol).self) {
        //     TTSManager.shared as any TTSManagerProtocol
        // }

        // 注册成就管理器协议
        // DependencyContainer.shared.registerSingleton((any AchievementManagerProtocol).self) {
        //     AchievementManager.shared as any AchievementManagerProtocol
        // }

        // 注册语音识别管理器协议
        // DependencyContainer.shared.registerSingleton((any SpeechRecognitionManagerProtocol).self) {
        //     SpeechRecognitionManager() as any SpeechRecognitionManagerProtocol
        // }

        // 注意：管理器的注册在各自的模块扩展中进行，避免重复注册
    }
}

// MARK: - 模块特定扩展

// 评估模块依赖项注册
public extension DependencyRegistry {
    static func registerEvaluationDependencies() {
        // 注册评估本地数据源
        DependencyContainer.shared.registerSingleton(EvaluationLocalDataSource.self) {
            return EvaluationLocalDataSource.shared
        }

        // 注册评估远程数据源
        DependencyContainer.shared.registerSingleton(EvaluationRemoteDataSource.self) {
            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
            return EvaluationRemoteDataSource(apiClient: apiClient)
        }

        // 注册评估仓库
        DependencyContainer.shared.registerSingleton(AnyEvaluationRepository.self) {
            let remoteDataSource = DependencyContainer.shared.resolve(EvaluationRemoteDataSource.self)
            let localDataSource = DependencyContainer.shared.resolve(EvaluationLocalDataSource.self)
            return EvaluationRepository(remoteDataSource: remoteDataSource, localDataSource: localDataSource)
        }

        // 注册评估仓库协议（为了向后兼容）
        DependencyContainer.shared.registerSingleton((any EvaluationRepositoryProtocol).self) {
            return DependencyContainer.shared.resolve(AnyEvaluationRepository.self)
        }

        // 注册评估管理器
        DependencyContainer.shared.registerSingleton(EvaluationManager.self) {
            let repository = DependencyContainer.shared.resolve((any EvaluationRepositoryProtocol).self)
            return EvaluationManager(repository: repository)
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 注册评估管理器协议
        // DependencyContainer.shared.registerSingleton((any EvaluationManagerProtocol).self) {
        //     return DependencyContainer.shared.resolve(EvaluationManager.self) as any EvaluationManagerProtocol
        // }

        // 注册 API 数据源管理器
        DependencyContainer.shared.registerSingleton(APIDataSourceManager.self) {
            return APIDataSourceManager.shared
        }

    }

    /// 注册课程模块依赖项
    static func registerLessonDependencies() {
        // 注册课程本地数据源
        DependencyContainer.shared.registerSingleton(LessonLocalDataSourceProtocol.self) {
            return LessonLocalDataSource()
        }

        // 注册课程远程数据源
        DependencyContainer.shared.registerSingleton(LessonRemoteDataSourceProtocol.self) {
            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
            return LessonRemoteDataSource(apiClient: apiClient)
        }

        // 注册课程仓库
        DependencyContainer.shared.registerSingleton(LessonRepositoryProtocol.self) {
            return LessonRepository.shared // Use the shared instance
        }

        // 注册课程管理器（使用仓库）
        DependencyContainer.shared.registerSingleton(LessonManager.self) {
            let repository = DependencyContainer.shared.resolve(LessonRepositoryProtocol.self)
            return LessonManager(repository: repository)
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 注册课程管理器协议
        // DependencyContainer.shared.registerSingleton((any LessonManagerProtocol).self) {
        //     return DependencyContainer.shared.resolve(LessonManager.self) as any LessonManagerProtocol
        // }
    }

    /// 注册词汇模块依赖项
    static func registerVocabularyDependencies() {
        // 注册词汇本地数据源
        DependencyContainer.shared.registerSingleton(VocabularyLocalDataSourceProtocol.self) {
            return VocabularyLocalDataSource()
        }

        // 注册词汇远程数据源
        DependencyContainer.shared.registerSingleton(VocabularyRemoteDataSourceProtocol.self) {
            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
            return VocabularyRemoteDataSource(apiClient: apiClient)
        }

        // 注册词汇仓库
        DependencyContainer.shared.registerSingleton(VocabularyRepositoryProtocol.self) {
            let remoteDataSource = DependencyContainer.shared.resolve(VocabularyRemoteDataSourceProtocol.self)
            let localDataSource = DependencyContainer.shared.resolve(VocabularyLocalDataSourceProtocol.self)
            return VocabularyRepository(remoteDataSource: remoteDataSource, localDataSource: localDataSource)
        }

        // 注册词汇管理器（使用仓库）
        DependencyContainer.shared.registerSingleton(VocabularyManager.self) {
            let repository = DependencyContainer.shared.resolve(VocabularyRepositoryProtocol.self)
            return VocabularyManager(repository: repository)
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 注册词汇管理器协议
        // DependencyContainer.shared.registerSingleton((any VocabularyManagerProtocol).self) {
        //     return DependencyContainer.shared.resolve(VocabularyManager.self) as any VocabularyManagerProtocol
        // }
    }
}

// 练习模块依赖项注册
public extension DependencyRegistry {
    static func registerPracticeDependencies() {
        // 注册练习会话本地数据源
        DependencyContainer.shared.registerSingleton(PracticeLocalDataSource.self) {
            return PracticeLocalDataSource.shared
        }

        // 注册练习会话远程数据源
        DependencyContainer.shared.registerSingleton(PracticeRemoteDataSource.self) {
            let apiClient = DependencyContainer.shared.resolve(APIClientProtocol.self)
            return PracticeRemoteDataSource(apiClient: apiClient)
        }

        // 注册练习会话仓库
        DependencyContainer.shared.registerSingleton(AnyPracticeRepository.self) {
            let localDataSource = DependencyContainer.shared.resolve(PracticeLocalDataSource.self)
            let remoteDataSource = DependencyContainer.shared.resolve(PracticeRemoteDataSource.self)
            return PracticeRepository(
                localDataSource: localDataSource,
                remoteDataSource: remoteDataSource
            )
        }

        // 注册练习管理器
        DependencyContainer.shared.registerSingleton(PracticeManager.self) {
            let repository = DependencyContainer.shared.resolve(AnyPracticeRepository.self)
            return PracticeManager(repository: repository)
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 注册练习管理器协议
        // DependencyContainer.shared.registerSingleton((any PracticeManagerProtocol).self) {
        //     return DependencyContainer.shared.resolve(PracticeManager.self) as any PracticeManagerProtocol
        // }
    }
}

// 用户模块依赖项注册
public extension DependencyRegistry {
    static func registerUserDependencies() {
        // 注册用户管理器
        DependencyContainer.shared.registerSingleton(UserManager.self) {
            return UserManager.shared
        }

        // TODO: 渐进式重构 - 稍后添加协议注册
        // 注册用户管理器协议
        // DependencyContainer.shared.registerSingleton((any UserManagerProtocol).self) {
        //     return UserManager.shared as any UserManagerProtocol
        // }
    }
}