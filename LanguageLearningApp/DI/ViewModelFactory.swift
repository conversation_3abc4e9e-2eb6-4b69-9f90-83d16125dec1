import Foundation
import SwiftUI

/// ViewModelFactory 协议定义
/// 负责创建所有 ViewModel 实例，确保依赖注入的一致性
public protocol ViewModelFactory: ObservableObject {
    // MARK: - 词汇学习相关
    func makeWordLearningViewModel() -> WordLearningViewModel

    // MARK: - 听力练习相关
    func makeListeningViewModel() -> ListeningViewModel

    // MARK: - 口语练习相关
    func makeSpeakingViewModel() -> SpeakingViewModel

    // MARK: - 语法练习相关
    func makeGrammarViewModel() -> GrammarViewModel

    // MARK: - 每日练习相关
    func makeDailyPracticeViewModel() -> DailyPracticeViewModel
    func makePersonalizedPracticeViewModel() -> PersonalizedPracticeViewModel
    func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel
    func makeProgressTrackingViewModel() -> ProgressTrackingViewModel

    // MARK: - 评估相关
    func makeEvaluationViewModel() -> EvaluationViewModel
    func makeEvaluationResultViewModel() -> EvaluationResultViewModel
    func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel

    // MARK: - 成就相关
    func makeAchievementViewModel() -> AchievementViewModel

    // MARK: - 用户相关
    // 注意：这些 ViewModel 暂时不存在，需要时再添加
    // func makeProfileViewModel() -> ProfileViewModel
    // func makeLoginViewModel() -> LoginViewModel
    // func makeRegistrationViewModel() -> RegistrationViewModel

    // MARK: - 设置相关
    // 注意：这些 ViewModel 暂时不存在，需要时再添加
    // func makeSettingsViewModel() -> SettingsViewModel
    // func makeLanguageSettingsViewModel() -> LanguageSettingsViewModel
    // func makeNotificationSettingsViewModel() -> NotificationSettingsViewModel
    // func makeAccountSettingsViewModel() -> AccountSettingsViewModel
    // func makeThemeSettingsViewModel() -> ThemeSettingsViewModel
}

/// 默认 ViewModelFactory 实现
/// 使用依赖注入容器来创建 ViewModel 实例
public class DefaultViewModelFactory: ViewModelFactory {
    private let container: DependencyContainer

    public init(container: DependencyContainer = .shared) {
        self.container = container
    }

    // MARK: - 词汇学习相关

    public func makeWordLearningViewModel() -> WordLearningViewModel {
        return WordLearningViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self),
            ttsManager: container.resolve(TTSManagerProtocol.self)
        )
    }



    // MARK: - 听力练习相关

    public func makeListeningViewModel() -> ListeningViewModel {
        return ListeningViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            ttsManager: container.resolve(TTSManagerProtocol.self)
        )
    }

    // MARK: - 口语练习相关

    public func makeSpeakingViewModel() -> SpeakingViewModel {
        return SpeakingViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            speechRecognitionManager: container.resolve(SpeechRecognitionManagerProtocol.self)
        )
    }

    // MARK: - 语法练习相关

    public func makeGrammarViewModel() -> GrammarViewModel {
        return GrammarViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self)
        )
    }

    // MARK: - 每日练习相关

    public func makeDailyPracticeViewModel() -> DailyPracticeViewModel {
        return DailyPracticeViewModel(
            vocabularyManager: container.resolve(VocabularyManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self),
            practiceManager: container.resolve(PracticeManagerProtocol.self)
        )
    }

    public func makePersonalizedPracticeViewModel() -> PersonalizedPracticeViewModel {
        return PersonalizedPracticeViewModel(
            personalizedLearningService: container.resolve(PersonalizedLearningServiceProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    public func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel {
        return DailyPracticeDashboardViewModel(
            personalizedLearningService: container.resolve(PersonalizedLearningServiceProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    public func makeProgressTrackingViewModel() -> ProgressTrackingViewModel {
        return ProgressTrackingViewModel(
            personalizedLearningService: container.resolve(PersonalizedLearningServiceProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    // MARK: - 评估相关

    public func makeEvaluationViewModel() -> EvaluationViewModel {
        return EvaluationViewModel(
            evaluationManager: container.resolve(EvaluationManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    public func makeEvaluationResultViewModel() -> EvaluationResultViewModel {
        return EvaluationResultViewModel(
            evaluationManager: container.resolve(EvaluationManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    public func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel {
        return EvaluationEntryViewModel(
            evaluationManager: container.resolve(EvaluationManagerProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    // MARK: - 成就相关

    public func makeAchievementViewModel() -> AchievementViewModel {
        return AchievementViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: container.resolve(UserManagerProtocol.self),
            lessonManager: container.resolve(LessonManagerProtocol.self),
            storageManager: container.resolve(StorageManagerProtocol.self),
            errorManager: container.resolve(ErrorManagerProtocol.self)
        )
    }

    // MARK: - 用户相关
    // 注意：这些 ViewModel 暂时不存在，需要时再添加实现

    // MARK: - 设置相关
    // 注意：这些 ViewModel 暂时不存在，需要时再添加实现
}

/// ViewModelFactory 的环境键
struct ViewModelFactoryKey: EnvironmentKey {
    static let defaultValue: ViewModelFactory = DefaultViewModelFactory()
}

extension EnvironmentValues {
    var viewModelFactory: ViewModelFactory {
        get { self[ViewModelFactoryKey.self] }
        set { self[ViewModelFactoryKey.self] = newValue }
    }
}
