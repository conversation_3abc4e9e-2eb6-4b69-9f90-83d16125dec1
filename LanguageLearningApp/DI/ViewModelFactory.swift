import Foundation
import SwiftUI

/// ViewModelFactory 协议定义
/// 负责创建所有 ViewModel 实例，确保依赖注入的一致性
/// 采用渐进式重构，先实现基础功能
protocol ViewModelFactory: ObservableObject {
    // MARK: - 基础 ViewModel 创建方法
    // 暂时简化，逐步添加更多 ViewModel

    // 注意：暂时注释掉复杂的 ViewModel，先确保编译通过
    // func makeWordLearningViewModel() -> WordLearningViewModel
    // func makeListeningViewModel() -> ListeningViewModel
    // func makeSpeakingViewModel() -> SpeakingViewModel
    // func makeGrammarViewModel() -> GrammarViewModel
    // func makeDailyPracticeViewModel() -> DailyPracticeViewModel
    // func makePersonalizedPracticeViewModel() -> PersonalizedPracticeViewModel
    // func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel
    // func makeProgressTrackingViewModel() -> ProgressTrackingViewModel
    // func makeEvaluationViewModel() -> EvaluationViewModel
    // func makeEvaluationResultViewModel() -> EvaluationResultViewModel
    // func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel
    // func makeAchievementViewModel() -> AchievementViewModel
}

/// 默认 ViewModelFactory 实现
/// 使用依赖注入容器来创建 ViewModel 实例
/// 采用渐进式重构，先实现基础功能
class DefaultViewModelFactory: ViewModelFactory {
    private let container: DependencyContainer

    init(container: DependencyContainer = .shared) {
        self.container = container
    }

    // MARK: - 基础实现
    // 暂时为空，逐步添加 ViewModel 创建方法

    // TODO: 渐进式添加 ViewModel 创建方法
    // 1. 先添加最简单的 ViewModel
    // 2. 逐步完善依赖注入
    // 3. 确保每次添加后都能编译通过
}

/// ViewModelFactory 的环境键
struct ViewModelFactoryKey: EnvironmentKey {
    static let defaultValue: any ViewModelFactory = DefaultViewModelFactory()
}

extension EnvironmentValues {
    var viewModelFactory: any ViewModelFactory {
        get { self[ViewModelFactoryKey.self] }
        set { self[ViewModelFactoryKey.self] = newValue }
    }
}
