import Foundation
import SwiftUI

/// ViewModelFactory 协议定义
/// 负责创建所有 ViewModel 实例，确保依赖注入的一致性
/// 采用渐进式重构，先实现基础功能
protocol ViewModelFactory: ObservableObject {
    // MARK: - 基础 ViewModel 创建方法
    // 渐进式添加 ViewModel 支持

    // Phase 1B: 开始添加简单的 ViewModel
    func makeWordLearningViewModel() -> WordLearningViewModel
    func makeAchievementViewModel() -> AchievementViewModel
    func makeDailyPracticeViewModel() -> DailyPracticeViewModel

    // TODO: 逐步添加更多 ViewModel
    // func makeListeningViewModel() -> ListeningViewModel
    // func makeSpeakingViewModel() -> SpeakingViewModel
    // func makeGrammarViewModel() -> GrammarViewModel
    // func makePersonalizedPracticeViewModel() -> PersonalizedPracticeViewModel
    // func makeDailyPracticeDashboardViewModel() -> DailyPracticeDashboardViewModel
    // func makeProgressTrackingViewModel() -> ProgressTrackingViewModel
    // func makeEvaluationViewModel() -> EvaluationViewModel
    // func makeEvaluationResultViewModel() -> EvaluationResultViewModel
    // func makeEvaluationEntryViewModel() -> EvaluationEntryViewModel
}

/// 默认 ViewModelFactory 实现
/// 使用依赖注入容器来创建 ViewModel 实例
/// 采用渐进式重构，先实现基础功能
@MainActor
class DefaultViewModelFactory: ViewModelFactory {
    private let container: DependencyContainer

    init(container: DependencyContainer = .shared) {
        self.container = container
    }

    // MARK: - 基础实现
    // Phase 1B: 渐进式添加 ViewModel 创建方法

    func makeWordLearningViewModel() -> WordLearningViewModel {
        return WordLearningViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: UserManager.shared, // 暂时使用具体类型
            errorManager: ErrorManager.shared, // 暂时使用具体类型
            ttsManager: TTSManager.shared // 暂时使用具体类型
        )
    }

    func makeAchievementViewModel() -> AchievementViewModel {
        return AchievementViewModel(
            networkService: container.resolve(NetworkServiceProtocol.self),
            userManager: UserManager.shared // 暂时使用具体类型
        )
    }

    func makeDailyPracticeViewModel() -> DailyPracticeViewModel {
        return DailyPracticeViewModel(
            vocabularyManager: VocabularyManager.shared, // 暂时使用具体类型
            errorManager: ErrorManager.shared, // 暂时使用具体类型
            practiceManager: container.resolve(PracticeManager.self) // 从容器解析
        )
    }

    // TODO: 继续添加更多 ViewModel
    // 1. 确保每次添加后都能编译通过
    // 2. 逐步完善依赖注入
    // 3. 替换具体类型为协议类型
}

/// ViewModelFactory 的环境键
struct ViewModelFactoryKey: EnvironmentKey {
    @MainActor
    static let defaultValue: any ViewModelFactory = DefaultViewModelFactory()
}

extension EnvironmentValues {
    var viewModelFactory: any ViewModelFactory {
        get { self[ViewModelFactoryKey.self] }
        set { self[ViewModelFactoryKey.self] = newValue }
    }
}
