import Foundation

/// 应用程序错误类型
enum AppError: Error, Equatable {
    // 网络错误
    case networkError(String)
    case serverError(String)
    case requestTimeout
    case noInternetConnection
    case apiError(String)

    // API 特定错误
    case invalidResponse
    case decodingError(Error)
    case unauthorized
    case notFound
    case badRequest(String)
    case forbidden
    case conflict
    case tooManyRequests
    case internalServerError
    case badGateway
    case serviceUnavailable
    case gatewayTimeout
    case unknown

    // 数据错误
    case dataLoadFailed(String)
    case dataSaveFailed(String)
    case dataNotFound
    case invalidData(String)

    // 用户错误
    case authenticationFailed(String)
    case userNotFound
    case invalidCredentials
    case registrationFailed(String)

    // 音频错误
    case audioPlaybackFailed(String)
    case audioRecordingFailed(String)
    case speechRecognitionFailed(String)
    case microphonePermissionDenied

    // 其他错误
    case unknownError
    case notImplemented
    case operationCancelled

    /// 自定义错误
    case customError(String)

    /// 获取用户友好的错误消息
    var localizedDescription: String {
        let localizationManager = LocalizationManager.shared

        switch self {
        // 网络错误
        case .networkError(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorNetwork)): \(message)"
        case .serverError(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorServer)): \(message)"
        case .requestTimeout:
            return localizationManager.localizedString(LocalizationKey.errorTimeout)
        case .noInternetConnection:
            return localizationManager.localizedString(LocalizationKey.errorNoInternet)
        case .apiError(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorAPI)): \(message)"

        // API 特定错误
        case .invalidResponse:
            return localizationManager.localizedString(LocalizationKey.errorInvalidResponse)
        case .decodingError(let error):
            return "\(localizationManager.localizedString(LocalizationKey.errorDecoding)): \(error.localizedDescription)"
        case .unauthorized:
            return localizationManager.localizedString(LocalizationKey.errorUnauthorized)
        case .notFound:
            return localizationManager.localizedString(LocalizationKey.errorNotFound)
        case .badRequest(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorBadRequest)): \(message)"
        case .forbidden:
            return localizationManager.localizedString(LocalizationKey.errorForbidden)
        case .conflict:
            return localizationManager.localizedString(LocalizationKey.errorConflict)
        case .tooManyRequests:
            return localizationManager.localizedString(LocalizationKey.errorTooManyRequests)
        case .internalServerError:
            return localizationManager.localizedString(LocalizationKey.errorInternalServer)
        case .badGateway:
            return localizationManager.localizedString(LocalizationKey.errorBadGateway)
        case .serviceUnavailable:
            return localizationManager.localizedString(LocalizationKey.errorServiceUnavailable)
        case .gatewayTimeout:
            return localizationManager.localizedString(LocalizationKey.errorGatewayTimeout)
        case .unknown:
            return localizationManager.localizedString(LocalizationKey.errorUnknown)

        // 数据错误
        case .dataLoadFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorData)): \(message)"
        case .dataSaveFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorDataSave)): \(message)"
        case .dataNotFound:
            return localizationManager.localizedString(LocalizationKey.errorDataNotFound)
        case .invalidData(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorInvalidData)): \(message)"

        // 用户错误
        case .authenticationFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorAuth)): \(message)"
        case .userNotFound:
            return localizationManager.localizedString(LocalizationKey.errorUserNotFound)
        case .invalidCredentials:
            return localizationManager.localizedString(LocalizationKey.errorInvalidCredentials)
        case .registrationFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorRegistration)): \(message)"

        // 音频错误
        case .audioPlaybackFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorAudio)): \(message)"
        case .audioRecordingFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorAudioRecording)): \(message)"
        case .speechRecognitionFailed(let message):
            return "\(localizationManager.localizedString(LocalizationKey.errorSpeech)): \(message)"
        case .microphonePermissionDenied:
            return localizationManager.localizedString(LocalizationKey.errorMicrophone)

        // 其他错误
        case .customError(let message):
            return message
        case .unknownError:
            return localizationManager.localizedString(LocalizationKey.errorUnknown)
        case .notImplemented:
            return localizationManager.localizedString(LocalizationKey.errorNotImplemented)
        case .operationCancelled:
            return localizationManager.localizedString(LocalizationKey.errorCancelled)
        }
    }

    /// 获取错误的严重程度
    var severity: ErrorSeverity {
        switch self {
        case .networkError, .serverError, .requestTimeout, .noInternetConnection, .apiError,
             .invalidResponse, .decodingError, .notFound, .badRequest, .forbidden, .conflict,
             .tooManyRequests, .internalServerError, .badGateway, .serviceUnavailable, .gatewayTimeout:
            return .warning

        case .dataLoadFailed, .dataSaveFailed, .dataNotFound, .invalidData:
            return .warning

        case .authenticationFailed, .userNotFound, .invalidCredentials, .registrationFailed, .unauthorized:
            return .error

        case .audioPlaybackFailed, .audioRecordingFailed, .speechRecognitionFailed, .microphonePermissionDenied:
            return .warning

        case .unknownError, .notImplemented, .unknown:
            return .error

        case .operationCancelled:
            return .info

        case .customError:
            return .error
        }
    }

    /// 从其他错误类型转换为 AppError
    static func from(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }

        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet:
                return .noInternetConnection
            case .timedOut:
                return .requestTimeout
            case .badServerResponse:
                return .serverError("Bad server response: \(urlError.localizedDescription)")
            default:
                return .networkError(urlError.localizedDescription)
            }
        }

        if let decodingError = error as? DecodingError {
            return .decodingError(decodingError)
        }

        return .unknown
    }

    // Manually implement Equatable
    static func == (lhs: AppError, rhs: AppError) -> Bool {
        switch (lhs, rhs) {
        case (.networkError(let lMsg), .networkError(let rMsg)):
            return lMsg == rMsg
        case (.serverError(let lMsg), .serverError(let rMsg)):
            return lMsg == rMsg
        case (.requestTimeout, .requestTimeout):
            return true
        case (.noInternetConnection, .noInternetConnection):
            return true
        case (.apiError(let lMsg), .apiError(let rMsg)):
            return lMsg == rMsg
        case (.invalidResponse, .invalidResponse):
            return true
        case (.decodingError(let lErr), .decodingError(let rErr)):
            // Comparing localizedDescription is a simple way, though not always perfect
            return lErr.localizedDescription == rErr.localizedDescription
        case (.unauthorized, .unauthorized):
            return true
        case (.notFound, .notFound):
            return true
        case (.badRequest(let lMsg), .badRequest(let rMsg)):
            return lMsg == rMsg
        case (.forbidden, .forbidden):
            return true
        case (.conflict, .conflict):
            return true
        case (.tooManyRequests, .tooManyRequests):
            return true
        case (.internalServerError, .internalServerError):
            return true
        case (.badGateway, .badGateway):
            return true
        case (.serviceUnavailable, .serviceUnavailable):
            return true
        case (.gatewayTimeout, .gatewayTimeout):
            return true
        case (.unknown, .unknown): // This case was duplicated, ensuring it's handled once.
            return true
        case (.dataLoadFailed(let lMsg), .dataLoadFailed(let rMsg)):
            return lMsg == rMsg
        case (.dataSaveFailed(let lMsg), .dataSaveFailed(let rMsg)):
            return lMsg == rMsg
        case (.dataNotFound, .dataNotFound):
            return true
        case (.invalidData(let lMsg), .invalidData(let rMsg)):
            return lMsg == rMsg
        case (.authenticationFailed(let lMsg), .authenticationFailed(let rMsg)):
            return lMsg == rMsg
        case (.userNotFound, .userNotFound):
            return true
        case (.invalidCredentials, .invalidCredentials):
            return true
        case (.registrationFailed(let lMsg), .registrationFailed(let rMsg)):
            return lMsg == rMsg
        case (.audioPlaybackFailed(let lMsg), .audioPlaybackFailed(let rMsg)):
            return lMsg == rMsg
        case (.audioRecordingFailed(let lMsg), .audioRecordingFailed(let rMsg)):
            return lMsg == rMsg
        case (.speechRecognitionFailed(let lMsg), .speechRecognitionFailed(let rMsg)):
            return lMsg == rMsg
        case (.microphonePermissionDenied, .microphonePermissionDenied):
            return true
        case (.unknownError, .unknownError): // This was .unknown before, changed to .unknownError to match enum case
            return true
        case (.notImplemented, .notImplemented):
            return true
        case (.operationCancelled, .operationCancelled):
            return true
        case (.customError(let lMsg), .customError(let rMsg)):
            return lMsg == rMsg
        // Add default case to catch any future cases or combinations not explicitly handled
        // However, for enums, it's often better to list all cases to get compiler help
        // if new cases are added. If all cases are covered, this default isn't strictly needed.
        // If a new case is added and not handled here, this will cause a compile error, which is good.
        default:
            // This default case handles any combinations not explicitly matched above.
            // This might occur if one side is a specific case and the other is different,
            // or if new cases are added without updating this function.
            // For strict equality, unmatched pairs should return false.
            return false
        }
    }
}

/// 错误严重程度
enum ErrorSeverity {
    case info
    case warning
    case error

    /// 获取对应的图标名称
    var iconName: String {
        switch self {
        case .info:
            return "info.circle"
        case .warning:
            return "exclamationmark.triangle"
        case .error:
            return "xmark.octagon"
        }
    }

    /// 获取对应的颜色
    var color: String {
        switch self {
        case .info:
            return "007AFF" // 蓝色
        case .warning:
            return "FF9500" // 橙色
        case .error:
            return "FF3B30" // 红色
        }
    }
}
