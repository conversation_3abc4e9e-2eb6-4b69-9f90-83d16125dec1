import Foundation
import SwiftUI
import Combine

/// 错误处理中间件，统一处理应用中的所有错误
@MainActor
public class ErrorHandler: ObservableObject {
    public static let shared = ErrorHandler()
    
    // MARK: - Published Properties
    @Published public private(set) var currentError: AppError?
    @Published public private(set) var isShowingError = false
    @Published public private(set) var errorHistory: [ErrorRecord] = []
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let maxErrorHistoryCount = 50
    
    // MARK: - Error Handling Configuration
    public struct ErrorHandlingConfig {
        let shouldLogErrors: Bool
        let shouldShowUserFriendlyMessages: Bool
        let shouldTrackErrorMetrics: Bool
        let autoHideErrorAfter: TimeInterval?
        
        public init(
            shouldLogErrors: Bool = true,
            shouldShowUserFriendlyMessages: Bool = true,
            shouldTrackErrorMetrics: Bool = true,
            autoHideErrorAfter: TimeInterval? = 5.0
        ) {
            self.shouldLogErrors = shouldLogErrors
            self.shouldShowUserFriendlyMessages = shouldShowUserFriendlyMessages
            self.shouldTrackErrorMetrics = shouldTrackErrorMetrics
            self.autoHideErrorAfter = autoHideErrorAfter
        }
    }
    
    private var config = ErrorHandlingConfig()
    
    private init() {
        setupErrorHandling()
    }
    
    // MARK: - Public Methods
    
    /// 配置错误处理
    public func configure(_ config: ErrorHandlingConfig) {
        self.config = config
    }
    
    /// 处理错误
    public func handle(_ error: Error, context: String? = nil) {
        let appError = AppError.from(error)
        handle(appError, context: context)
    }
    
    /// 处理 AppError
    public func handle(_ error: AppError, context: String? = nil) {
        let errorRecord = ErrorRecord(
            error: error,
            context: context,
            timestamp: Date()
        )
        
        // 记录错误
        recordError(errorRecord)
        
        // 日志记录
        if config.shouldLogErrors {
            logError(errorRecord)
        }
        
        // 显示用户友好的错误消息
        if config.shouldShowUserFriendlyMessages {
            showError(error)
        }
        
        // 错误指标追踪
        if config.shouldTrackErrorMetrics {
            trackErrorMetrics(errorRecord)
        }
    }
    
    /// 清除当前错误
    public func clearCurrentError() {
        currentError = nil
        isShowingError = false
    }
    
    /// 清除错误历史
    public func clearErrorHistory() {
        errorHistory.removeAll()
    }
    
    /// 获取特定类型的错误统计
    public func getErrorStats(for errorType: AppError) -> ErrorStats {
        let matchingErrors = errorHistory.filter { record in
            // 简化的错误类型匹配
            return type(of: record.error) == type(of: errorType)
        }
        
        return ErrorStats(
            totalCount: matchingErrors.count,
            lastOccurrence: matchingErrors.last?.timestamp,
            frequency: calculateFrequency(for: matchingErrors)
        )
    }
    
    // MARK: - Private Methods
    
    private func setupErrorHandling() {
        // 自动隐藏错误
        $isShowingError
            .filter { $0 }
            .sink { [weak self] _ in
                guard let self = self,
                      let autoHideDelay = self.config.autoHideErrorAfter else { return }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + autoHideDelay) {
                    self.clearCurrentError()
                }
            }
            .store(in: &cancellables)
    }
    
    private func recordError(_ errorRecord: ErrorRecord) {
        errorHistory.append(errorRecord)
        
        // 限制历史记录数量
        if errorHistory.count > maxErrorHistoryCount {
            errorHistory.removeFirst(errorHistory.count - maxErrorHistoryCount)
        }
    }
    
    private func logError(_ errorRecord: ErrorRecord) {
        let logMessage = """
        [ERROR] \(errorRecord.timestamp)
        Type: \(type(of: errorRecord.error))
        Message: \(errorRecord.error.localizedDescription)
        Context: \(errorRecord.context ?? "N/A")
        Severity: \(errorRecord.error.severity)
        """
        
        print(logMessage)
        
        // 在生产环境中，这里可以集成第三方日志服务
        // 例如：Firebase Crashlytics, Sentry 等
    }
    
    private func showError(_ error: AppError) {
        currentError = error
        isShowingError = true
    }
    
    private func trackErrorMetrics(_ errorRecord: ErrorRecord) {
        // 在生产环境中，这里可以集成分析服务
        // 例如：Firebase Analytics, Mixpanel 等
        
        let eventName = "error_occurred"
        let parameters = [
            "error_type": String(describing: type(of: errorRecord.error)),
            "error_severity": errorRecord.error.severity.rawValue,
            "context": errorRecord.context ?? "unknown"
        ]
        
        print("[ANALYTICS] \(eventName): \(parameters)")
    }
    
    private func calculateFrequency(for errors: [ErrorRecord]) -> Double {
        guard errors.count > 1 else { return 0.0 }
        
        let timeSpan = errors.last!.timestamp.timeIntervalSince(errors.first!.timestamp)
        return Double(errors.count) / max(timeSpan / 3600, 1.0) // 每小时频率
    }
}

// MARK: - Supporting Types

/// 错误记录
public struct ErrorRecord {
    let error: AppError
    let context: String?
    let timestamp: Date
    
    public init(error: AppError, context: String?, timestamp: Date) {
        self.error = error
        self.context = context
        self.timestamp = timestamp
    }
}

/// 错误统计
public struct ErrorStats {
    let totalCount: Int
    let lastOccurrence: Date?
    let frequency: Double // 每小时频率
    
    public init(totalCount: Int, lastOccurrence: Date?, frequency: Double) {
        self.totalCount = totalCount
        self.lastOccurrence = lastOccurrence
        self.frequency = frequency
    }
}

// MARK: - ErrorSeverity Extension

extension ErrorSeverity {
    var rawValue: String {
        switch self {
        case .info:
            return "info"
        case .warning:
            return "warning"
        case .error:
            return "error"
        }
    }
}

// MARK: - View Extensions for Error Handling

extension View {
    /// 添加错误处理到视图
    public func errorHandling() -> some View {
        self.modifier(ErrorHandlingModifier())
    }
}

/// 错误处理视图修饰符
struct ErrorHandlingModifier: ViewModifier {
    @StateObject private var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        content
            .alert("错误", isPresented: $errorHandler.isShowingError) {
                Button("确定") {
                    errorHandler.clearCurrentError()
                }
            } message: {
                if let error = errorHandler.currentError {
                    Text(error.localizedDescription)
                }
            }
    }
}
