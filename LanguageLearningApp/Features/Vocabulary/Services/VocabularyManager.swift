import Foundation
import Combine
import SwiftUI

/// 词汇管理器，处理词汇相关的业务逻辑
public class VocabularyManager: ObservableObject {
    // MARK: - 单例
    public static let shared = VocabularyManager()

    // MARK: - Published Properties
    @Published public private(set) var words: [Word] = []
    @Published public private(set) var categories: [VocabularyCategory] = []
    @Published public private(set) var learnedWords: Set<UUID> = []
    @Published public private(set) var favoriteWords: Set<UUID> = []
    @Published public private(set) var wordProgress: [UUID: WordProgress] = [:]
    @Published public var searchText: String = "" {
        didSet {
            filterWords()
        }
    }
    @Published public private(set) var filteredWords: [Word] = []
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Private Properties
    private let repository: VocabularyRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    public init(repository: VocabularyRepositoryProtocol = VocabularyRepository.shared) {
        self.repository = repository
        setupSubscriptions()
        loadInitialData()
    }

    // MARK: - Setup
    private func setupSubscriptions() {
        // 监听搜索文本变化
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.filterWords()
            }
            .store(in: &cancellables)
    }

    private func loadInitialData() {
        Task {
            await loadWords()
            await loadCategories()
            await loadProgress()
            await loadLearnedWords()
            await loadFavoriteWords()
        }
    }

    // MARK: - Public Methods

    /// 加载词汇列表
    @MainActor
    public func loadWords(category: String? = nil, difficulty: String? = nil) async {
        isLoading = true
        error = nil

        do {
            let loadedWords = try await repository.getWords(category: category, difficulty: difficulty)
            self.words = loadedWords
            self.filteredWords = loadedWords
        } catch {
            self.error = error
            print("Failed to load words: \(error)")
        }

        isLoading = false
    }

    /// 获取词汇详情
    public func getWordDetail(id: UUID) async throws -> Word {
        return try await repository.getWordDetail(id: id)
    }

    /// 加载词汇分类
    @MainActor
    public func loadCategories() async {
        do {
            let loadedCategories = try await repository.getCategories()
            self.categories = loadedCategories
        } catch {
            self.error = error
            print("Failed to load categories: \(error)")
        }
    }

    /// 加载学习进度
    @MainActor
    public func loadProgress() async {
        do {
            let allProgress = try await repository.getAllWordProgress()
            self.wordProgress = Dictionary(uniqueKeysWithValues: allProgress.map { ($0.wordId, $0) })
        } catch {
            self.error = error
            print("Failed to load progress: \(error)")
        }
    }

    /// 更新词汇学习进度
    @MainActor
    public func updateWordProgress(wordId: UUID, correct: Bool, timeSpent: TimeInterval) async {
        do {
            let updatedProgress = try await repository.updateWordProgress(
                wordId: wordId,
                correct: correct,
                timeSpent: timeSpent
            )
            self.wordProgress[wordId] = updatedProgress

            // 如果达到学习标准，标记为已学习
            if updatedProgress.masteryLevel >= 0.8 {
                self.learnedWords.insert(wordId)
            }
        } catch {
            self.error = error
            print("Failed to update word progress: \(error)")
        }
    }

    /// 加载已学习词汇
    @MainActor
    public func loadLearnedWords() async {
        do {
            let learned = try await repository.getLearnedWords()
            self.learnedWords = Set(learned.map { $0.id })
        } catch {
            self.error = error
            print("Failed to load learned words: \(error)")
        }
    }

    /// 标记词汇为已学习
    @MainActor
    public func markWordAsLearned(wordId: UUID) async {
        do {
            let success = try await repository.markWordAsLearned(wordId: wordId)
            if success {
                self.learnedWords.insert(wordId)
            }
        } catch {
            self.error = error
            print("Failed to mark word as learned: \(error)")
        }
    }

    /// 加载收藏词汇
    @MainActor
    public func loadFavoriteWords() async {
        do {
            let favorites = try await repository.getFavoriteWords()
            self.favoriteWords = Set(favorites.map { $0.id })
        } catch {
            self.error = error
            print("Failed to load favorite words: \(error)")
        }
    }

    /// 切换收藏状态
    @MainActor
    public func toggleFavorite(wordId: UUID) async {
        let isFavorite = favoriteWords.contains(wordId)

        do {
            let success = try await repository.toggleFavoriteWord(id: wordId, isFavorite: !isFavorite)
            if success {
                if isFavorite {
                    favoriteWords.remove(wordId)
                } else {
                    favoriteWords.insert(wordId)
                }
            }
        } catch {
            self.error = error
            print("Failed to toggle favorite: \(error)")
        }
    }

    /// 搜索词汇
    public func searchWords(query: String) {
        searchText = query
    }

    /// 按分类筛选词汇
    @MainActor
    public func filterByCategory(_ category: String?) {
        Task {
            await loadWords(category: category)
        }
    }

    /// 按难度筛选词汇
    @MainActor
    public func filterByDifficulty(_ difficulty: String?) {
        Task {
            await loadWords(difficulty: difficulty)
        }
    }

    /// 获取推荐学习词汇
    public func getRecommendedWords(limit: Int = 10) -> [Word] {
        // 基于学习进度和遗忘曲线推荐词汇
        let unlearnedWords = words.filter { !learnedWords.contains($0.id) }
        let needReviewWords = words.filter { word in
            if let progress = wordProgress[word.id] {
                // 检查是否需要复习（基于遗忘曲线）
                let daysSinceLastReview = Calendar.current.dateComponents([.day], from: progress.lastReviewedAt, to: Date()).day ?? 0
                let reviewInterval = calculateReviewInterval(masteryLevel: progress.masteryLevel)
                return daysSinceLastReview >= reviewInterval
            }
            return false
        }

        // 优先推荐需要复习的词汇，然后是未学习的词汇
        let recommendedWords = Array((needReviewWords + unlearnedWords).prefix(limit))
        return recommendedWords
    }

    /// 获取学习统计
    public func getLearningStats() -> VocabularyLearningStats {
        let totalWords = words.count
        let learnedCount = learnedWords.count
        let favoriteCount = favoriteWords.count

        let averageMastery = wordProgress.values.isEmpty ? 0.0 :
            wordProgress.values.map { $0.masteryLevel }.reduce(0, +) / Double(wordProgress.count)

        return VocabularyLearningStats(
            totalWords: totalWords,
            learnedWords: learnedCount,
            favoriteWords: favoriteCount,
            averageMasteryLevel: averageMastery,
            studyStreak: calculateStudyStreak()
        )
    }

    /// 检查词汇是否已学习
    public func isWordLearned(wordId: UUID) -> Bool {
        return learnedWords.contains(wordId)
    }

    /// 检查词汇是否收藏
    public func isWordFavorite(wordId: UUID) -> Bool {
        return favoriteWords.contains(wordId)
    }

    /// 获取词汇进度
    public func getWordProgress(wordId: UUID) -> WordProgress? {
        return wordProgress[wordId]
    }

    // MARK: - Private Methods

    private func filterWords() {
        if searchText.isEmpty {
            filteredWords = words
        } else {
            filteredWords = words.filter { word in
                word.text.localizedCaseInsensitiveContains(searchText) ||
                word.translation.localizedCaseInsensitiveContains(searchText) ||
                word.exampleSentence.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    private func calculateReviewInterval(masteryLevel: Double) -> Int {
        // 基于掌握程度计算复习间隔（天数）
        switch masteryLevel {
        case 0.0..<0.3:
            return 1 // 1天后复习
        case 0.3..<0.6:
            return 3 // 3天后复习
        case 0.6..<0.8:
            return 7 // 1周后复习
        case 0.8..<0.9:
            return 14 // 2周后复习
        default:
            return 30 // 1个月后复习
        }
    }

    private func calculateStudyStreak() -> Int {
        // 计算连续学习天数
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streak = 0
        var currentDate = today

        // 检查最近的学习记录
        let sortedProgress = wordProgress.values.sorted { $0.lastReviewedAt > $1.lastReviewedAt }

        for progress in sortedProgress {
            let progressDate = calendar.startOfDay(for: progress.lastReviewedAt)
            if calendar.isDate(progressDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }

        return streak
    }
}

// MARK: - Combine Support

extension VocabularyManager {
    /// 获取词汇列表的发布者
    public func getWordsPublisher(category: String? = nil, difficulty: String? = nil) -> AnyPublisher<[Word], Error> {
        return repository.getWordsPublisher(category: category, difficulty: difficulty)
    }

    /// 获取词汇详情的发布者
    public func getWordDetailPublisher(id: UUID) -> AnyPublisher<Word, Error> {
        return repository.getWordDetailPublisher(id: id)
    }
}

// MARK: - Supporting Models

public struct VocabularyCategory: Identifiable, Codable {
    public let id: UUID
    public let name: String
    public let description: String
    public let iconName: String
    public let color: String
    public let wordCount: Int

    public init(id: UUID, name: String, description: String, iconName: String, color: String, wordCount: Int) {
        self.id = id
        self.name = name
        self.description = description
        self.iconName = iconName
        self.color = color
        self.wordCount = wordCount
    }
}

public struct VocabularyLearningStats: Codable {
    public let totalWords: Int
    public let learnedWords: Int
    public let favoriteWords: Int
    public let averageMasteryLevel: Double
    public let studyStreak: Int

    public init(totalWords: Int, learnedWords: Int, favoriteWords: Int, averageMasteryLevel: Double, studyStreak: Int) {
        self.totalWords = totalWords
        self.learnedWords = learnedWords
        self.favoriteWords = favoriteWords
        self.averageMasteryLevel = averageMasteryLevel
        self.studyStreak = studyStreak
    }
}
