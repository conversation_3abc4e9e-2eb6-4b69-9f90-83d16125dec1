import Foundation
import Combine
import SwiftUI

/// 评估管理器，处理评估相关的业务逻辑
public class EvaluationManager: ObservableObject {
    // MARK: - Published Properties
    @Published public private(set) var availableEvaluations: [Evaluation] = []
    @Published public private(set) var currentEvaluation: Evaluation?
    @Published public private(set) var evaluationResult: EvaluationResult?
    @Published public private(set) var isLoading = false
    @Published public var error: Error?

    // MARK: - Private Properties
    private let repository: any EvaluationRepositoryProtocol
    private var cancellables = Set<AnyCancellable>()
    private var currentUserID: UUID?

    // MARK: - Initialization
    public init(repository: any EvaluationRepositoryProtocol) {
        self.repository = repository
    }

    // MARK: - Public Methods
    public func setCurrentUser(userID: UUID) {
        self.currentUserID = userID
    }

    public func loadAvailableEvaluations() {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return
        }

        self.isLoading = true
        self.error = nil

        repository.getAvailableEvaluations()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] evaluations in
                self?.availableEvaluations = evaluations
            }
            .store(in: &cancellables)
    }

    /// 加载评估详情
    /// - Parameter id: 评估ID
    public func loadEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.getById(id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] evaluation in
                guard let self = self else { return }

                if let evaluation = evaluation {
                    self.currentEvaluation = evaluation
                } else {
                    self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法加载评估"])
                }
            }
            .store(in: &cancellables)
    }

    /// 开始评估
    /// - Parameter id: 评估ID
    public func startEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.startEvaluation(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] evaluation in
                self?.currentEvaluation = evaluation
            }
            .store(in: &cancellables)
    }

    /// 提交答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    public func submitAnswer(evaluationId: UUID, questionId: UUID, answer: String) {
        self.isLoading = true
        self.error = nil

        repository.submitAnswer(id: evaluationId, questionId: questionId, answer: answer)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] evaluation in
                self?.currentEvaluation = evaluation
            }
            .store(in: &cancellables)
    }

    /// 完成评估
    /// - Parameter id: 评估ID
    public func completeEvaluation(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.completeEvaluation(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] result in
                self?.evaluationResult = result
            }
            .store(in: &cancellables)
    }

    /// 加载评估历史
    public func loadEvaluationHistory() {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return
        }

        self.isLoading = true
        self.error = nil

        repository.getUserEvaluationHistory(userID: userID)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] results in
                let evaluations = results.map { result -> Evaluation in
                    return Evaluation(
                        id: result.id,
                        userID: result.userID,
                        type: .placement,
                        category: .vocabulary,
                        status: .completed,
                        title: "评估结果",
                        description: "评估结果",
                        passingScore: 0,
                        duration: 0,
                        totalQuestions: 0,
                        sections: [],
                        isStarted: true,
                        isCompleted: true,
                        createdAt: result.createdAt,
                        updatedAt: result.completedAt
                    )
                }

                self?.availableEvaluations = evaluations
            }
            .store(in: &cancellables)
    }

    /// 加载评估结果
    /// - Parameter id: 评估ID
    public func loadEvaluationResult(id: UUID) {
        self.isLoading = true
        self.error = nil

        repository.getEvaluationResult(id: id)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] result in
                self?.evaluationResult = result
            }
            .store(in: &cancellables)
    }

    /// 创建新评估
    /// - Parameter completion: 完成回调
    public func createEvaluation(completion: @escaping (UUID?) -> Void) {
        repository.createEvaluation()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completionStatus in
                if case .failure(let error) = completionStatus {
                    self?.error = error
                    completion(nil)
                }
            } receiveValue: { evaluationId in
                completion(evaluationId)
            }
            .store(in: &cancellables)
    }

    /// 清除本地缓存
    public func clearLocalCache() {
        repository.clearLocalCache()
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    self?.error = error
                }
            } receiveValue: { [weak self] success in
                if success {
                    self?.availableEvaluations = []
                    self?.currentEvaluation = nil
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 异步方法

    /// 异步加载可用评估列表
    /// - Returns: 是否成功
    @MainActor
    public func loadAvailableEvaluationsAsync() async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluations = try await repository.getAvailableEvaluationsAsync()
            availableEvaluations = evaluations
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估详情
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            if let evaluation = try await repository.getByIdAsync(id) {
                currentEvaluation = evaluation
                self.isLoading = false
                return true
            } else {
                self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法加载评估"])
                self.isLoading = false
                return false
            }
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步开始评估
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func startEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluation = try await repository.startEvaluationAsync(id: id)
            currentEvaluation = evaluation
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步提交答案
    /// - Parameters:
    ///   - evaluationId: 评估ID
    ///   - questionId: 问题ID
    ///   - answer: 用户答案
    /// - Returns: 是否成功
    @MainActor
    public func submitAnswerAsync(evaluationId: UUID, questionId: UUID, answer: String) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let evaluation = try await repository.submitAnswerAsync(id: evaluationId, questionId: questionId, answer: answer)
            currentEvaluation = evaluation
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步完成评估
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func completeEvaluationAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let result = try await repository.completeEvaluationAsync(id: id)
            evaluationResult = result
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估历史
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationHistoryAsync() async -> Bool {
        guard let userID = currentUserID else {
            self.error = NSError(domain: "EvaluationManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "未设置当前用户"])
            return false
        }

        self.isLoading = true
        self.error = nil

        do {
            _ = try await repository.getUserEvaluationHistoryAsync(userID: userID)
            availableEvaluations = []
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步加载评估结果
    /// - Parameter id: 评估ID
    /// - Returns: 是否成功
    @MainActor
    public func loadEvaluationResultAsync(id: UUID) async -> Bool {
        self.isLoading = true
        self.error = nil

        do {
            let result = try await repository.getEvaluationResultAsync(id: id)
            evaluationResult = result
            self.isLoading = false
            return true
        } catch {
            self.error = error
            self.isLoading = false
            return false
        }
    }

    /// 异步创建新评估
    /// - Returns: 新评估ID（如果成功）
    @MainActor
    public func createEvaluationAsync() async -> UUID? {
        do {
            return try await repository.createEvaluationAsync()
        } catch {
            self.error = error
            return nil
        }
    }

    /// 异步清除本地缓存
    /// - Returns: 是否成功
    @MainActor
    public func clearLocalCacheAsync() async -> Bool {
        do {
            let success = try await repository.clearLocalCacheAsync()
            if success {
                availableEvaluations = []
                currentEvaluation = nil
            }
            return success
        } catch {
            self.error = error
            return false
        }
    }

    /// 获取评估完成百分比
    /// - Parameter evaluation: 评估
    /// - Returns: 完成百分比
    public func getCompletionPercentage(for evaluation: Evaluation) -> Double {
        return evaluation.completionPercentage
    }

    /// 获取评估剩余时间（格式化）
    /// - Parameter evaluation: 评估
    /// - Returns: 格式化的剩余时间
    public func getRemainingTimeFormatted(for evaluation: Evaluation) -> String {
        guard let remainingSeconds = evaluation.remainingTime() else {
            return "已完成"
        }

        let minutes = Int(remainingSeconds) / 60
        let seconds = Int(remainingSeconds) % 60

        return String(format: "%02d:%02d", minutes, seconds)
    }

    /// 检查评估是否已完成所有问题
    /// - Parameter evaluation: 评估
    /// - Returns: 是否完成所有问题
    public func hasAnsweredAllQuestions(for evaluation: Evaluation) -> Bool {
        return evaluation.completedQuestionsCount >= evaluation.totalQuestions
    }
}
