import SwiftUI
import Combine
#if canImport(UIKit)
import UIKit
#else
import AppKit
#endif

/// 错误提示视图
struct ErrorView: View {
    // 使用 ObservedObject 观察变化
    @ObservedObject private var errorManager = ErrorManager.shared
    @StateObject private var localizationManager = LocalizationManager.shared
    @State private var showCopiedMessage = false

    var body: some View {
        if let error = errorManager.currentError {
            HStack(spacing: 12) {
                // Error icon
                Image(systemName: error.severity.iconName)
                    .foregroundColor(severityColor(error.severity))
                    .font(.system(size: AppTheme.Dimensions.iconSizeSmall, weight: .semibold))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(AppTheme.Colors.card)
                            .overlay(
                                Circle()
                                    .stroke(severityColor(error.severity).opacity(0.5), lineWidth: 1)
                            )
                    )

                // Error content
                VStack(alignment: .leading, spacing: 4) {
                    Text(errorTitle(for: error))
                        .font(AppTheme.Typography.subheadline.weight(.bold))
                        .foregroundColor(AppTheme.Colors.textPrimary)

                    Text(error.localizedDescription)
                        .font(AppTheme.Typography.footnote.weight(.medium))
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)
                        .textSelection(.enabled)
                }

                Spacer()

                // Action buttons
                HStack(spacing: 8) {
                    // Copy button
                    Button(action: {
                        #if canImport(UIKit)
                        UIPasteboard.general.string = error.localizedDescription
                        #else
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(error.localizedDescription, forType: .string)
                        #endif

                        withAnimation {
                            showCopiedMessage = true
                        }
                        // Hide copied message after 2 seconds
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            withAnimation {
                                showCopiedMessage = false
                            }
                        }
                    }) {
                        Image(systemName: "doc.on.doc")
                            .font(AppTheme.Typography.caption1.weight(.medium))
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .frame(width: 24, height: 24)
                    }

                    // Close button
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            errorManager.clearError()
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(AppTheme.Typography.caption1.weight(.medium))
                            .foregroundColor(AppTheme.Colors.textSecondary)
                            .frame(width: 24, height: 24)
                    }
                }
            }
            .padding(.vertical, AppTheme.Dimensions.paddingSmall + 2)
            .padding(.horizontal, AppTheme.Dimensions.paddingSmall + 4)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                    .fill(AppTheme.Colors.background)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.Dimensions.cornerRadiusSmall)
                            .stroke(
                                severityColor(error.severity).opacity(0.3),
                                lineWidth: 1
                            )
                    )
            )
            .shadow(color: severityColor(error.severity).opacity(0.15), radius: 6, x: 0, y: 3)
            .padding(.horizontal, AppTheme.Dimensions.paddingSmall + 4)
            .padding(.top, 6)
            .transition(.move(edge: .top).combined(with: .opacity))
            .overlay(
                Group {
                    if showCopiedMessage {
                        Text(localizationManager.localizedString(LocalizationKey.error_copied))
                            .font(AppTheme.Typography.caption2.weight(.medium))
                            .foregroundColor(AppTheme.Colors.textPrimary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 3)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(AppTheme.Colors.card)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 4)
                                            .stroke(Color.white.opacity(0.1), lineWidth: 0.5)
                                    )
                            )
                            .offset(x: 70, y: -25)
                            .transition(.scale.combined(with: .opacity))
                    }
                }
            )
            .onAppear {
                // Auto-dismiss after 5 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        errorManager.clearError()
                    }
                }
            }
        }
    }

    /// 根据错误类型获取标题
    private func errorTitle(for error: AppError) -> String {
        switch error {
        case .networkError, .serverError, .requestTimeout, .noInternetConnection, .apiError, .invalidResponse,
             .badGateway, .serviceUnavailable, .gatewayTimeout:
            return localizationManager.localizedString(LocalizationKey.error_type_network)

        case .dataLoadFailed, .dataSaveFailed, .dataNotFound, .invalidData, .decodingError, .notFound, .badRequest:
            return localizationManager.localizedString(LocalizationKey.error_type_data)

        case .authenticationFailed, .userNotFound, .invalidCredentials, .registrationFailed, .unauthorized,
             .forbidden, .conflict, .tooManyRequests:
            return localizationManager.localizedString(LocalizationKey.error_type_user)

        case .audioPlaybackFailed, .audioRecordingFailed, .speechRecognitionFailed, .microphonePermissionDenied:
            return localizationManager.localizedString(LocalizationKey.error_type_audio)

        case .unknownError, .notImplemented, .operationCancelled, .unknown, .internalServerError:
            return localizationManager.localizedString(LocalizationKey.error_type_system)

        case .customError:
            return localizationManager.localizedString(LocalizationKey.error)
        }
    }

    /// 根據錯誤嚴重程度獲取顏色
    private func severityColor(_ severity: ErrorSeverity) -> Color {
        switch severity {
        case .info:
            return AppTheme.Colors.textPrimary
        case .warning:
            return AppTheme.Colors.accent2
        case .error:
            return AppTheme.Colors.error
        }
    }
}

/// Global error view modifier
struct ErrorViewModifier: ViewModifier {
    // Use ObservedObject to observe changes
    @ObservedObject private var errorManager = ErrorManager.shared

    func body(content: Content) -> some View {
        ZStack {
            content

            VStack {
                if errorManager.showError && errorManager.currentError != nil {
                    ErrorView()
                        .zIndex(100)
                        .transition(.asymmetric(
                            insertion: .move(edge: .top).combined(with: .opacity),
                            removal: .opacity.animation(.easeOut(duration: 0.2))
                        ))
                }

                Spacer()
            }
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: errorManager.showError)
        }
    }
}

extension View {
    /// Add global error handling to a view
    func withErrorHandling() -> some View {
        self.modifier(ErrorViewModifier())
    }
}

// 颜色扩展已移至 Color+Hex.swift

#Preview {
    ZStack {
        // Background gradient
        LinearGradient(
            gradient: Gradient(colors: [
                AppTheme.Colors.background,
                AppTheme.Colors.backgroundSecondary
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()

        VStack {
            Text("App Content")
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundColor(.white)
                .padding()

            Button("Show Network Error") {
                ErrorManager.shared.showError(.networkError("Unable to connect to server. Please check your network connection."))
            }
            .padding()
            .background(AppTheme.Colors.card)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
    }
    .environmentObject(ErrorManager.shared)
    .onAppear {
        // Show error after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            ErrorManager.shared.showError(.networkError("Unable to connect to server. Please check your network connection."))
        }
    }
    .withErrorHandling()
}
